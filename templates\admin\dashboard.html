{% extends "layouts/base.html" %}

{% block title %}Admin Dashboard - Blog{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="admin-sidebar p-3">
                <h5 class="text-white mb-3">
                    <i class="fas fa-cog me-2"></i>Admin Panel
                </h5>
                <nav class="nav flex-column">
                    <a class="nav-link active" href="{{ url_for('admin.dashboard') }}">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link" href="{{ url_for('admin.posts') }}">
                        <i class="fas fa-blog me-2"></i>Posts
                    </a>
                    <a class="nav-link" href="{{ url_for('admin.categories') }}">
                        <i class="fas fa-tags me-2"></i>Categories
                    </a>
                    <a class="nav-link" href="{{ url_for('admin.comments') }}">
                        <i class="fas fa-comments me-2"></i>Comments
                    </a>
                    <a class="nav-link" href="{{ url_for('admin.users') }}">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Dashboard</h1>
                <a href="{{ url_for('admin.new_post') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>New Post
                </a>
            </div>

            <!-- Statistics Cards -->
            <div class="row admin-stats mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h3>{{ stats.total_posts }}</h3>
                                    <p class="mb-0">Total Posts</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-blog fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-white" style="background: linear-gradient(135deg, #28a745, #20c997);">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h3>{{ stats.published_posts }}</h3>
                                    <p class="mb-0">Published</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-white" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h3>{{ stats.draft_posts }}</h3>
                                    <p class="mb-0">Drafts</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-edit fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card text-white" style="background: linear-gradient(135deg, #17a2b8, #6f42c1);">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h3>{{ stats.total_users }}</h3>
                                    <p class="mb-0">Users</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Recent Posts -->
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-blog me-2"></i>Recent Posts
                            </h5>
                            <a href="{{ url_for('admin.posts') }}" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="card-body">
                            {% if recent_posts %}
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Title</th>
                                                <th>Status</th>
                                                <th>Author</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for post in recent_posts %}
                                            <tr>
                                                <td>
                                                    <a href="{{ url_for('main.post_detail', slug=post.slug) }}" 
                                                       class="text-decoration-none">
                                                        {{ post.title[:50] }}{% if post.title|length > 50 %}...{% endif %}
                                                    </a>
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ 'success' if post.status == 'published' else 'warning' }}">
                                                        {{ post.status.title() }}
                                                    </span>
                                                </td>
                                                <td>{{ post.author.username }}</td>
                                                <td>{{ post.created_at.strftime('%b %d, %Y') }}</td>
                                                <td>
                                                    <a href="{{ url_for('admin.edit_post', post_id=post.id) }}" 
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <p class="text-muted text-center py-4">No posts yet.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Recent Comments -->
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-comments me-2"></i>Recent Comments
                            </h5>
                            <a href="{{ url_for('admin.comments') }}" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="card-body">
                            {% if recent_comments %}
                                {% for comment in recent_comments %}
                                <div class="d-flex mb-3">
                                    <img src="{{ url_for('static', filename='images/' + comment.author.avatar) }}" 
                                         alt="{{ comment.author.get_full_name() }}" class="rounded-circle me-3" 
                                         width="40" height="40"
                                         onerror="this.src='{{ url_for('static', filename='images/default-avatar.png') }}'">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ comment.author.get_full_name() }}</h6>
                                        <p class="mb-1 small">{{ comment.content[:100] }}{% if comment.content|length > 100 %}...{% endif %}</p>
                                        <small class="text-muted">
                                            on <a href="{{ url_for('main.post_detail', slug=comment.post.slug) }}" 
                                                  class="text-decoration-none">{{ comment.post.title[:30] }}...</a>
                                        </small>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <p class="text-muted text-center py-4">No comments yet.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>Quick Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('admin.new_post') }}" class="btn btn-primary w-100">
                                        <i class="fas fa-plus me-2"></i>Create Post
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('admin.new_category') }}" class="btn btn-success w-100">
                                        <i class="fas fa-tag me-2"></i>Add Category
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('admin.comments', status='pending') }}" class="btn btn-warning w-100">
                                        <i class="fas fa-clock me-2"></i>Pending Comments
                                        {% if stats.pending_comments > 0 %}
                                        <span class="badge bg-light text-dark ms-1">{{ stats.pending_comments }}</span>
                                        {% endif %}
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="{{ url_for('admin.users') }}" class="btn btn-info w-100">
                                        <i class="fas fa-users me-2"></i>Manage Users
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
