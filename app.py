from flask import Flask, render_template, request, url_for
from flask_login import LoginManager
from flask_sitemap import Sitemap
from flask_migrate import Migrate
from dotenv import load_dotenv
import os
from datetime import datetime
from database import db

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'your-secret-key-here')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL',
    'sqlite:///blog.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# SEO Configuration
app.config['SITE_NAME'] = 'MyBlog'
app.config['SITE_DESCRIPTION'] = 'A beautiful and responsive blog application built with Flask'
app.config['SITE_URL'] = os.environ.get('SITE_URL', 'http://localhost:5000')
app.config['SITE_AUTHOR'] = 'MyBlog Team'
app.config['SITE_KEYWORDS'] = 'blog, flask, python, web development, articles, posts'

# Initialize extensions
db.init_app(app)
migrate = Migrate(app, db)
sitemap = Sitemap(app=app)

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Please log in to access this page.'

# Import models after db initialization
from models import User, Post, Comment, Category

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Import and register blueprints
from routes.main import main_bp
from routes.auth import auth_bp
from routes.admin import admin_bp
from routes.api import api_bp

app.register_blueprint(main_bp)
app.register_blueprint(auth_bp, url_prefix='/auth')
app.register_blueprint(admin_bp, url_prefix='/admin')
app.register_blueprint(api_bp, url_prefix='/api')

# SEO and Template Context Processors
@app.context_processor
def inject_seo_data():
    return {
        'site_name': app.config['SITE_NAME'],
        'site_description': app.config['SITE_DESCRIPTION'],
        'site_url': app.config['SITE_URL'],
        'site_author': app.config['SITE_AUTHOR'],
        'site_keywords': app.config['SITE_KEYWORDS'],
        'current_year': datetime.now().year
    }

# Sitemap routes
@sitemap.register_generator
def index():
    yield 'main.index', {}
    yield 'main.about', {}
    yield 'main.contact', {}

@sitemap.register_generator
def posts():
    for post in Post.query.filter_by(status='published').all():
        yield 'main.post_detail', {'slug': post.slug}, post.updated_at

@sitemap.register_generator
def categories():
    for category in Category.query.all():
        yield 'main.category_posts', {'slug': category.slug}

# SEO Meta Tags Template Filter
@app.template_filter('truncate_words')
def truncate_words(text, length=20):
    words = text.split()
    if len(words) > length:
        return ' '.join(words[:length]) + '...'
    return text

# Robots.txt
@app.route('/robots.txt')
def robots_txt():
    return render_template('robots.txt'), 200, {'Content-Type': 'text/plain'}

# Error handlers with SEO-friendly pages
@app.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    db.session.rollback()
    return render_template('errors/500.html'), 500

# Create database tables
with app.app_context():
    db.create_all()
    
    # Create admin user if it doesn't exist
    admin_user = User.query.filter_by(email='<EMAIL>').first()
    if not admin_user:
        from werkzeug.security import generate_password_hash
        admin = User(
            username='admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123'),
            is_admin=True
        )
        db.session.add(admin)
        db.session.commit()
        print("Admin user created: <EMAIL> / admin123")

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
