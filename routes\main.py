from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models import Post, Category, Comment, User
from database import db
from datetime import datetime
import re

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    page = request.args.get('page', 1, type=int)
    category_id = request.args.get('category', type=int)
    search = request.args.get('search', '')
    
    # Base query for published posts
    query = Post.query.filter_by(status='published')
    
    # Filter by category if specified
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    # Search functionality
    if search:
        query = query.filter(Post.title.contains(search) | Post.content.contains(search))
    
    # Order by published date
    query = query.order_by(Post.published_at.desc())
    
    # Paginate results
    posts = query.paginate(
        page=page, per_page=6, error_out=False
    )
    
    # Get categories for sidebar
    categories = Category.query.all()
    
    # Get recent posts for sidebar
    recent_posts = Post.query.filter_by(status='published').order_by(Post.published_at.desc()).limit(5).all()
    
    return render_template('main/index.html', 
                         posts=posts, 
                         categories=categories, 
                         recent_posts=recent_posts,
                         current_category=category_id,
                         search_query=search)

@main_bp.route('/post/<slug>')
def post_detail(slug):
    post = Post.query.filter_by(slug=slug, status='published').first_or_404()
    
    # Increment view count
    post.views += 1
    db.session.commit()
    
    # Get comments for this post
    comments = Comment.query.filter_by(post_id=post.id, is_approved=True, parent_id=None).order_by(Comment.created_at.desc()).all()
    
    # Get related posts (same category, excluding current post)
    related_posts = Post.query.filter_by(category_id=post.category_id, status='published').filter(Post.id != post.id).limit(3).all()
    
    return render_template('main/post_detail.html', 
                         post=post, 
                         comments=comments, 
                         related_posts=related_posts)

@main_bp.route('/category/<slug>')
def category_posts(slug):
    category = Category.query.filter_by(slug=slug).first_or_404()
    page = request.args.get('page', 1, type=int)
    
    posts = Post.query.filter_by(category_id=category.id, status='published').order_by(Post.published_at.desc()).paginate(
        page=page, per_page=6, error_out=False
    )
    
    return render_template('main/category_posts.html', posts=posts, category=category)

@main_bp.route('/add_comment', methods=['POST'])
@login_required
def add_comment():
    post_id = request.form.get('post_id')
    content = request.form.get('content')
    parent_id = request.form.get('parent_id')
    
    if not post_id or not content:
        flash('Comment content is required.', 'error')
        return redirect(request.referrer)
    
    post = Post.query.get_or_404(post_id)
    
    comment = Comment(
        content=content,
        author_id=current_user.id,
        post_id=post_id,
        parent_id=parent_id if parent_id else None
    )
    
    db.session.add(comment)
    db.session.commit()
    
    flash('Comment added successfully!', 'success')
    return redirect(url_for('main.post_detail', slug=post.slug))

@main_bp.route('/like_post/<int:post_id>', methods=['POST'])
@login_required
def like_post(post_id):
    post = Post.query.get_or_404(post_id)
    post.likes += 1
    db.session.commit()
    
    return jsonify({'likes': post.likes})

@main_bp.route('/about')
def about():
    return render_template('main/about.html')

@main_bp.route('/contact')
def contact():
    return render_template('main/contact.html')

@main_bp.route('/search')
def search():
    query = request.args.get('q', '')
    page = request.args.get('page', 1, type=int)
    
    if query:
        posts = Post.query.filter_by(status='published').filter(
            Post.title.contains(query) | Post.content.contains(query)
        ).order_by(Post.published_at.desc()).paginate(
            page=page, per_page=6, error_out=False
        )
    else:
        posts = None
    
    return render_template('main/search_results.html', posts=posts, query=query)
