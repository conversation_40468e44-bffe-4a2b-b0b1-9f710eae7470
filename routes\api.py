from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from models import Post, Comment
from database import db

api_bp = Blueprint('api', __name__)

@api_bp.route('/like_post/<int:post_id>', methods=['POST'])
@login_required
def like_post(post_id):
    post = Post.query.get_or_404(post_id)
    post.likes += 1
    db.session.commit()
    
    return jsonify({
        'success': True,
        'likes': post.likes
    })

@api_bp.route('/add_comment', methods=['POST'])
@login_required
def add_comment():
    data = request.get_json()
    
    post_id = data.get('post_id')
    content = data.get('content')
    parent_id = data.get('parent_id')
    
    if not post_id or not content:
        return jsonify({
            'success': False,
            'message': 'Post ID and content are required.'
        }), 400
    
    post = Post.query.get_or_404(post_id)
    
    comment = Comment(
        content=content,
        author_id=current_user.id,
        post_id=post_id,
        parent_id=parent_id if parent_id else None
    )
    
    db.session.add(comment)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': 'Comment added successfully!',
        'comment': {
            'id': comment.id,
            'content': comment.content,
            'author': comment.author.get_full_name(),
            'created_at': comment.created_at.strftime('%B %d, %Y at %I:%M %p')
        }
    })

@api_bp.route('/get_post_url/<int:post_id>')
def get_post_url(post_id):
    post = Post.query.get_or_404(post_id)
    
    return jsonify({
        'success': True,
        'url': request.host_url.rstrip('/') + '/post/' + post.slug,
        'title': post.title
    })
