from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from functools import wraps
from models import User, Post, Comment, Category
from database import db
from datetime import datetime
import os
from werkzeug.utils import secure_filename
import re

admin_bp = Blueprint('admin', __name__)

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash('You need admin privileges to access this page.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function

def create_slug(title):
    # Convert title to slug
    slug = re.sub(r'[^\w\s-]', '', title.lower())
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-')

@admin_bp.route('/')
@login_required
@admin_required
def dashboard():
    # Get statistics
    total_posts = Post.query.count()
    published_posts = Post.query.filter_by(status='published').count()
    draft_posts = Post.query.filter_by(status='draft').count()
    total_users = User.query.count()
    total_comments = Comment.query.count()
    pending_comments = Comment.query.filter_by(is_approved=False).count()
    
    # Get recent posts
    recent_posts = Post.query.order_by(Post.created_at.desc()).limit(5).all()
    
    # Get recent comments
    recent_comments = Comment.query.order_by(Comment.created_at.desc()).limit(5).all()
    
    stats = {
        'total_posts': total_posts,
        'published_posts': published_posts,
        'draft_posts': draft_posts,
        'total_users': total_users,
        'total_comments': total_comments,
        'pending_comments': pending_comments
    }
    
    return render_template('admin/dashboard.html', 
                         stats=stats, 
                         recent_posts=recent_posts, 
                         recent_comments=recent_comments)

@admin_bp.route('/posts')
@login_required
@admin_required
def posts():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', 'all')
    
    query = Post.query
    if status != 'all':
        query = query.filter_by(status=status)
    
    posts = query.order_by(Post.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template('admin/posts.html', posts=posts, current_status=status)

@admin_bp.route('/posts/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_post():
    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')
        excerpt = request.form.get('excerpt')
        category_id = request.form.get('category_id')
        status = request.form.get('status', 'draft')
        
        if not title or not content:
            flash('Title and content are required.', 'error')
            return render_template('admin/edit_post.html', categories=Category.query.all())
        
        # Create slug
        slug = create_slug(title)
        
        # Ensure slug is unique
        original_slug = slug
        counter = 1
        while Post.query.filter_by(slug=slug).first():
            slug = f"{original_slug}-{counter}"
            counter += 1
        
        post = Post(
            title=title,
            slug=slug,
            content=content,
            excerpt=excerpt,
            author_id=current_user.id,
            category_id=category_id if category_id else None,
            status=status
        )
        
        if status == 'published':
            post.published_at = datetime.utcnow()
        
        db.session.add(post)
        db.session.commit()
        
        flash('Post created successfully!', 'success')
        return redirect(url_for('admin.posts'))
    
    categories = Category.query.all()
    return render_template('admin/edit_post.html', categories=categories)

@admin_bp.route('/posts/edit/<int:post_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_post(post_id):
    post = Post.query.get_or_404(post_id)
    
    if request.method == 'POST':
        post.title = request.form.get('title')
        post.content = request.form.get('content')
        post.excerpt = request.form.get('excerpt')
        post.category_id = request.form.get('category_id') if request.form.get('category_id') else None
        old_status = post.status
        post.status = request.form.get('status', 'draft')
        
        # Update slug if title changed
        new_slug = create_slug(post.title)
        if new_slug != post.slug:
            # Ensure new slug is unique
            original_slug = new_slug
            counter = 1
            while Post.query.filter_by(slug=new_slug).filter(Post.id != post.id).first():
                new_slug = f"{original_slug}-{counter}"
                counter += 1
            post.slug = new_slug
        
        # Set published date if status changed to published
        if old_status != 'published' and post.status == 'published':
            post.published_at = datetime.utcnow()
        
        db.session.commit()
        flash('Post updated successfully!', 'success')
        return redirect(url_for('admin.posts'))
    
    categories = Category.query.all()
    return render_template('admin/edit_post.html', post=post, categories=categories)

@admin_bp.route('/posts/delete/<int:post_id>', methods=['POST'])
@login_required
@admin_required
def delete_post(post_id):
    post = Post.query.get_or_404(post_id)
    db.session.delete(post)
    db.session.commit()
    flash('Post deleted successfully!', 'success')
    return redirect(url_for('admin.posts'))

@admin_bp.route('/categories')
@login_required
@admin_required
def categories():
    categories = Category.query.all()
    return render_template('admin/categories.html', categories=categories)

@admin_bp.route('/categories/new', methods=['GET', 'POST'])
@login_required
@admin_required
def new_category():
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        color = request.form.get('color', '#007bff')
        
        if not name:
            flash('Category name is required.', 'error')
            return render_template('admin/edit_category.html')
        
        slug = create_slug(name)
        
        # Ensure slug is unique
        original_slug = slug
        counter = 1
        while Category.query.filter_by(slug=slug).first():
            slug = f"{original_slug}-{counter}"
            counter += 1
        
        category = Category(
            name=name,
            slug=slug,
            description=description,
            color=color
        )
        
        db.session.add(category)
        db.session.commit()
        
        flash('Category created successfully!', 'success')
        return redirect(url_for('admin.categories'))
    
    return render_template('admin/edit_category.html')

@admin_bp.route('/comments')
@login_required
@admin_required
def comments():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', 'all')
    
    query = Comment.query
    if status == 'pending':
        query = query.filter_by(is_approved=False)
    elif status == 'approved':
        query = query.filter_by(is_approved=True)
    
    comments = query.order_by(Comment.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/comments.html', comments=comments, current_status=status)

@admin_bp.route('/comments/approve/<int:comment_id>', methods=['POST'])
@login_required
@admin_required
def approve_comment(comment_id):
    comment = Comment.query.get_or_404(comment_id)
    comment.is_approved = True
    db.session.commit()
    flash('Comment approved!', 'success')
    return redirect(url_for('admin.comments'))

@admin_bp.route('/comments/delete/<int:comment_id>', methods=['POST'])
@login_required
@admin_required
def delete_comment(comment_id):
    comment = Comment.query.get_or_404(comment_id)
    db.session.delete(comment)
    db.session.commit()
    flash('Comment deleted!', 'success')
    return redirect(url_for('admin.comments'))

@admin_bp.route('/users')
@login_required
@admin_required
def users():
    page = request.args.get('page', 1, type=int)
    users = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    return render_template('admin/users.html', users=users)
