{% extends "layouts/base.html" %}

{% block title %}{{ site_name }} - {{ site_description }}{% endblock %}
{% block description %}{{ site_description }}. Discover amazing stories, insights, and ideas from our community of writers.{% endblock %}
{% block keywords %}{{ site_keywords }}{% endblock %}

{% block structured_data %}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "{{ site_name }}",
    "description": "{{ site_description }}",
    "url": "{{ site_url }}",
    "author": {
        "@type": "Organization",
        "name": "{{ site_author }}"
    },
    "publisher": {
        "@type": "Organization",
        "name": "{{ site_name }}",
        "logo": {
            "@type": "ImageObject",
            "url": "{{ url_for('static', filename='images/logo.png', _external=True) }}"
        }
    }
}
</script>
{% endblock %}

{% block content %}
<div class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-3">Welcome to MyBlog</h1>
                <p class="lead mb-4">Discover amazing stories, insights, and ideas from our community of writers.</p>
                {% if not current_user.is_authenticated %}
                <a href="{{ url_for('auth.register') }}" class="btn btn-light btn-lg">
                    <i class="fas fa-user-plus me-2"></i>Join Our Community
                </a>
                {% endif %}
            </div>
            <div class="col-md-4 text-center">
                <i class="fas fa-blog fa-5x opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<div class="container py-5">
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Search Bar -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('main.search') }}" class="d-flex">
                        <input type="text" name="q" class="form-control me-2" placeholder="Search posts..." 
                               value="{{ search_query or '' }}">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Category Filter -->
            {% if categories %}
            <div class="mb-4">
                <div class="d-flex flex-wrap gap-2">
                    <a href="{{ url_for('main.index') }}" 
                       class="btn btn-sm {{ 'btn-primary' if not current_category else 'btn-outline-primary' }}">
                        All Posts
                    </a>
                    {% for category in categories %}
                    <a href="{{ url_for('main.index', category=category.id) }}" 
                       class="btn btn-sm {{ 'btn-primary' if current_category == category.id else 'btn-outline-primary' }}"
                       style="{% if current_category == category.id %}background-color: {{ category.color }}; border-color: {{ category.color }};{% endif %}">
                        {{ category.name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Blog Posts -->
            {% if posts.items %}
                <div class="row">
                    {% for post in posts.items %}
                    <div class="col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            {% if post.featured_image %}
                            <img src="{{ url_for('static', filename='uploads/' + post.featured_image) }}" 
                                 class="card-img-top" alt="{{ post.title }}" style="height: 200px; object-fit: cover;">
                            {% endif %}
                            <div class="card-body d-flex flex-column">
                                <div class="mb-2">
                                    {% if post.category %}
                                    <span class="badge" style="background-color: {{ post.category.color }};">
                                        {{ post.category.name }}
                                    </span>
                                    {% endif %}
                                    <small class="text-muted ms-2">
                                        <i class="fas fa-clock me-1"></i>{{ post.get_reading_time() }} min read
                                    </small>
                                </div>
                                <h5 class="card-title">
                                    <a href="{{ url_for('main.post_detail', slug=post.slug) }}" 
                                       class="text-decoration-none text-dark">
                                        {{ post.title }}
                                    </a>
                                </h5>
                                <p class="card-text text-muted flex-grow-1">{{ post.get_excerpt() }}</p>
                                <div class="mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>{{ post.author.get_full_name() }}
                                        </small>
                                        <small class="text-muted">
                                            {{ post.published_at.strftime('%b %d, %Y') if post.published_at else post.created_at.strftime('%b %d, %Y') }}
                                        </small>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i>{{ post.views }}
                                            <i class="fas fa-heart ms-2 me-1"></i>{{ post.likes }}
                                            <i class="fas fa-comments ms-2 me-1"></i>{{ post.comments|length }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if posts.pages > 1 %}
                <nav aria-label="Blog pagination">
                    <ul class="pagination justify-content-center">
                        {% if posts.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.index', page=posts.prev_num, category=current_category, search=search_query) }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in posts.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != posts.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main.index', page=page_num, category=current_category, search=search_query) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if posts.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.index', page=posts.next_num, category=current_category, search=search_query) }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                    <h4>No posts found</h4>
                    <p class="text-muted">
                        {% if search_query %}
                            No posts match your search criteria.
                        {% elif current_category %}
                            No posts in this category yet.
                        {% else %}
                            No posts have been published yet.
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Recent Posts -->
            {% if recent_posts %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5><i class="fas fa-clock me-2"></i>Recent Posts</h5>
                </div>
                <div class="card-body">
                    {% for post in recent_posts %}
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            {% if post.featured_image %}
                            <img src="{{ url_for('static', filename='uploads/' + post.featured_image) }}" 
                                 alt="{{ post.title }}" class="rounded" width="60" height="60" style="object-fit: cover;">
                            {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px;">
                                <i class="fas fa-blog text-muted"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">
                                <a href="{{ url_for('main.post_detail', slug=post.slug) }}" 
                                   class="text-decoration-none">
                                    {{ post.title[:50] }}{% if post.title|length > 50 %}...{% endif %}
                                </a>
                            </h6>
                            <small class="text-muted">
                                {{ post.published_at.strftime('%b %d') if post.published_at else post.created_at.strftime('%b %d') }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Categories -->
            {% if categories %}
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tags me-2"></i>Categories</h5>
                </div>
                <div class="card-body">
                    {% for category in categories %}
                    <a href="{{ url_for('main.index', category=category.id) }}" 
                       class="d-flex justify-content-between align-items-center text-decoration-none mb-2">
                        <span>
                            <i class="fas fa-tag me-2" style="color: {{ category.color }};"></i>
                            {{ category.name }}
                        </span>
                        <span class="badge bg-secondary">{{ category.posts|length }}</span>
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
