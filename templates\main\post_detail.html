{% extends "layouts/base.html" %}

{% block title %}{{ post.title }} - Blog{% endblock %}

{% block meta %}
<meta property="og:title" content="{{ post.title }}">
<meta property="og:description" content="{{ post.get_excerpt() }}">
<meta property="og:type" content="article">
<meta property="og:url" content="{{ request.url }}">
{% if post.featured_image %}
<meta property="og:image" content="{{ url_for('static', filename='uploads/' + post.featured_image, _external=True) }}">
{% endif %}
<meta property="article:author" content="{{ post.author.get_full_name() }}">
<meta property="article:published_time" content="{{ post.published_at.isoformat() if post.published_at else post.created_at.isoformat() }}">
{% if post.category %}
<meta property="article:section" content="{{ post.category.name }}">
{% endif %}
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8">
            <article class="card shadow-sm">
                {% if post.featured_image %}
                <img src="{{ url_for('static', filename='uploads/' + post.featured_image) }}" 
                     class="card-img-top" alt="{{ post.title }}" style="height: 400px; object-fit: cover;">
                {% endif %}
                
                <div class="card-body">
                    <!-- Post Meta -->
                    <div class="mb-3">
                        {% if post.category %}
                        <span class="badge" style="background-color: {{ post.category.color }};">
                            {{ post.category.name }}
                        </span>
                        {% endif %}
                        <small class="text-muted ms-2">
                            <i class="fas fa-clock me-1"></i>{{ post.get_reading_time() }} min read
                        </small>
                    </div>

                    <!-- Post Title -->
                    <h1 class="card-title mb-3">{{ post.title }}</h1>

                    <!-- Post Author and Date -->
                    <div class="d-flex align-items-center mb-4">
                        <img src="{{ url_for('static', filename='images/' + post.author.avatar) }}" 
                             alt="{{ post.author.get_full_name() }}" class="rounded-circle me-3" 
                             width="50" height="50"
                             onerror="this.src='{{ url_for('static', filename='images/default-avatar.png') }}'">
                        <div>
                            <h6 class="mb-0">{{ post.author.get_full_name() }}</h6>
                            <small class="text-muted">
                                {{ post.published_at.strftime('%B %d, %Y at %I:%M %p') if post.published_at else post.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                            </small>
                        </div>
                    </div>

                    <!-- Post Content -->
                    <div class="post-content">
                        {{ post.content|safe }}
                    </div>

                    <!-- Post Stats and Actions -->
                    <hr>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="text-muted me-3">
                                <i class="fas fa-eye me-1"></i>{{ post.views }} views
                            </span>
                            <span class="text-muted me-3">
                                <i class="fas fa-heart me-1"></i><span id="likes-count">{{ post.likes }}</span> likes
                            </span>
                            <span class="text-muted">
                                <i class="fas fa-comments me-1"></i>{{ post.comments|length }} comments
                            </span>
                        </div>
                        <div>
                            {% if current_user.is_authenticated %}
                            <button class="btn btn-outline-danger btn-sm" onclick="likePost({{ post.id }})">
                                <i class="fas fa-heart me-1"></i>Like
                            </button>
                            {% endif %}
                            <button class="btn btn-outline-primary btn-sm" onclick="sharePost({{ post.id }})">
                                <i class="fas fa-share me-1"></i>Share
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="copyLink('{{ url_for('main.post_detail', slug=post.slug, _external=True) }}')">
                                <i class="fas fa-link me-1"></i>Copy Link
                            </button>
                        </div>
                    </div>

                    <!-- Social Share Buttons -->
                    <div class="mt-3">
                        <h6>Share this post:</h6>
                        <div class="social-share-buttons">
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ url_for('main.post_detail', slug=post.slug, _external=True) }}" 
                               target="_blank" class="btn btn-primary btn-sm me-2">
                                <i class="fab fa-facebook-f me-1"></i>Facebook
                            </a>
                            <a href="https://twitter.com/intent/tweet?url={{ url_for('main.post_detail', slug=post.slug, _external=True) }}&text={{ post.title }}" 
                               target="_blank" class="btn btn-info btn-sm me-2">
                                <i class="fab fa-twitter me-1"></i>Twitter
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ url_for('main.post_detail', slug=post.slug, _external=True) }}" 
                               target="_blank" class="btn btn-primary btn-sm me-2">
                                <i class="fab fa-linkedin me-1"></i>LinkedIn
                            </a>
                            <a href="https://wa.me/?text={{ post.title }} {{ url_for('main.post_detail', slug=post.slug, _external=True) }}" 
                               target="_blank" class="btn btn-success btn-sm">
                                <i class="fab fa-whatsapp me-1"></i>WhatsApp
                            </a>
                        </div>
                    </div>
                </div>
            </article>

            <!-- Comments Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-comments me-2"></i>Comments ({{ comments|length }})</h5>
                </div>
                <div class="card-body">
                    {% if current_user.is_authenticated %}
                    <!-- Add Comment Form -->
                    <form method="POST" action="{{ url_for('main.add_comment') }}" class="mb-4">
                        <input type="hidden" name="post_id" value="{{ post.id }}">
                        <div class="mb-3">
                            <textarea name="content" class="form-control" rows="3" 
                                      placeholder="Write your comment..." required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Post Comment
                        </button>
                    </form>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <a href="{{ url_for('auth.login') }}">Login</a> to post a comment.
                    </div>
                    {% endif %}

                    <!-- Comments List -->
                    {% if comments %}
                        {% for comment in comments %}
                        <div class="comment mb-4">
                            <div class="d-flex">
                                <img src="{{ url_for('static', filename='images/' + comment.author.avatar) }}" 
                                     alt="{{ comment.author.get_full_name() }}" class="rounded-circle me-3" 
                                     width="40" height="40"
                                     onerror="this.src='{{ url_for('static', filename='images/default-avatar.png') }}'">
                                <div class="flex-grow-1">
                                    <div class="bg-light rounded p-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0">{{ comment.author.get_full_name() }}</h6>
                                            <small class="text-muted">
                                                {{ comment.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                            </small>
                                        </div>
                                        <p class="mb-0">{{ comment.content }}</p>
                                    </div>
                                    
                                    <!-- Reply Button -->
                                    {% if current_user.is_authenticated %}
                                    <button class="btn btn-link btn-sm text-muted mt-1" 
                                            onclick="toggleReplyForm({{ comment.id }})">
                                        <i class="fas fa-reply me-1"></i>Reply
                                    </button>
                                    
                                    <!-- Reply Form -->
                                    <div id="reply-form-{{ comment.id }}" class="mt-2" style="display: none;">
                                        <form method="POST" action="{{ url_for('main.add_comment') }}">
                                            <input type="hidden" name="post_id" value="{{ post.id }}">
                                            <input type="hidden" name="parent_id" value="{{ comment.id }}">
                                            <div class="mb-2">
                                                <textarea name="content" class="form-control" rows="2" 
                                                          placeholder="Write your reply..." required></textarea>
                                            </div>
                                            <button type="submit" class="btn btn-primary btn-sm">
                                                <i class="fas fa-paper-plane me-1"></i>Reply
                                            </button>
                                            <button type="button" class="btn btn-secondary btn-sm ms-2" 
                                                    onclick="toggleReplyForm({{ comment.id }})">
                                                Cancel
                                            </button>
                                        </form>
                                    </div>
                                    {% endif %}

                                    <!-- Replies -->
                                    {% if comment.replies %}
                                    <div class="ms-4 mt-3">
                                        {% for reply in comment.replies %}
                                        <div class="d-flex mb-3">
                                            <img src="{{ url_for('static', filename='images/' + reply.author.avatar) }}" 
                                                 alt="{{ reply.author.get_full_name() }}" class="rounded-circle me-3" 
                                                 width="32" height="32"
                                                 onerror="this.src='{{ url_for('static', filename='images/default-avatar.png') }}'">
                                            <div class="flex-grow-1">
                                                <div class="bg-light rounded p-2">
                                                    <div class="d-flex justify-content-between align-items-start mb-1">
                                                        <h6 class="mb-0 small">{{ reply.author.get_full_name() }}</h6>
                                                        <small class="text-muted">
                                                            {{ reply.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                                        </small>
                                                    </div>
                                                    <p class="mb-0 small">{{ reply.content }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted text-center py-4">
                            <i class="fas fa-comments fa-2x mb-3 d-block"></i>
                            No comments yet. Be the first to comment!
                        </p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Related Posts -->
            {% if related_posts %}
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-newspaper me-2"></i>Related Posts</h5>
                </div>
                <div class="card-body">
                    {% for related_post in related_posts %}
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            {% if related_post.featured_image %}
                            <img src="{{ url_for('static', filename='uploads/' + related_post.featured_image) }}" 
                                 alt="{{ related_post.title }}" class="rounded" width="60" height="60" style="object-fit: cover;">
                            {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="width: 60px; height: 60px;">
                                <i class="fas fa-blog text-muted"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">
                                <a href="{{ url_for('main.post_detail', slug=related_post.slug) }}" 
                                   class="text-decoration-none">
                                    {{ related_post.title[:50] }}{% if related_post.title|length > 50 %}...{% endif %}
                                </a>
                            </h6>
                            <small class="text-muted">
                                {{ related_post.published_at.strftime('%b %d') if related_post.published_at else related_post.created_at.strftime('%b %d') }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function likePost(postId) {
    fetch(`/api/like_post/${postId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('likes-count').textContent = data.likes;
        }
    })
    .catch(error => console.error('Error:', error));
}

function sharePost(postId) {
    if (navigator.share) {
        navigator.share({
            title: '{{ post.title }}',
            text: '{{ post.get_excerpt() }}',
            url: window.location.href
        });
    } else {
        copyLink(window.location.href);
    }
}

function copyLink(url) {
    navigator.clipboard.writeText(url).then(() => {
        alert('Link copied to clipboard!');
    });
}

function toggleReplyForm(commentId) {
    const form = document.getElementById(`reply-form-${commentId}`);
    form.style.display = form.style.display === 'none' ? 'block' : 'none';
}
</script>
{% endblock %}
