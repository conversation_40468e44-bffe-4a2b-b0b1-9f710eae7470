#!/usr/bin/env python3
"""
Test script to verify the blog application setup with latest Flask
"""

try:
    print("Testing imports...")

    # Test Flask imports
    from flask import Flask
    print("✓ Flask 3.1.0 imported successfully")

    from flask_sqlalchemy import SQLAlchemy
    print("✓ Flask-SQLAlchemy 3.1.1 imported successfully")

    from flask_login import LoginManager
    print("✓ Flask-Login imported successfully")

    from flask_sitemap import Sitemap
    print("✓ Flask-Sitemap imported successfully")

    from flask_migrate import Migrate
    print("✓ Flask-Migrate imported successfully")

    # Test our modules
    from database import db
    print("✓ Database module imported successfully")

    from models import User, Post, Comment, Category
    print("✓ Models imported successfully")
    
    # Test app creation
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    with app.app_context():
        db.create_all()
        print("✓ Database tables created successfully")
        
        # Test creating a user
        test_user = User(
            username='testuser',
            email='<EMAIL>'
        )
        test_user.set_password('testpass')
        
        db.session.add(test_user)
        db.session.commit()
        print("✓ Test user created successfully")
        
        # Test creating a category
        test_category = Category(
            name='Test Category',
            slug='test-category',
            description='A test category'
        )
        
        db.session.add(test_category)
        db.session.commit()
        print("✓ Test category created successfully")
        
        # Test creating a post
        test_post = Post(
            title='Test Post',
            slug='test-post',
            content='<p>This is a test post content.</p>',
            author_id=test_user.id,
            category_id=test_category.id,
            status='published'
        )
        
        db.session.add(test_post)
        db.session.commit()
        print("✓ Test post created successfully")
        
        # Test creating a comment
        test_comment = Comment(
            content='This is a test comment.',
            author_id=test_user.id,
            post_id=test_post.id
        )
        
        db.session.add(test_comment)
        db.session.commit()
        print("✓ Test comment created successfully")
        
        print("\n🎉 All tests passed! The blog application setup is working correctly.")
        print(f"📊 Database contains:")
        print(f"   - {User.query.count()} users")
        print(f"   - {Category.query.count()} categories") 
        print(f"   - {Post.query.count()} posts")
        print(f"   - {Comment.query.count()} comments")
        
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
