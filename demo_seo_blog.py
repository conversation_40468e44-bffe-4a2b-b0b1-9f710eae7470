#!/usr/bin/env python3
"""
Demo script for the SEO-optimized blog application with Flask 3.1.0
"""

import os
from datetime import datetime, timezone
from flask import Flask
from database import db
from models import User, Post, Comment, Category

def create_demo_data():
    """Create comprehensive demo data for the blog"""
    
    print("🚀 Creating demo data for SEO-optimized blog...")
    
    # Create categories
    categories = [
        Category(
            name='Web Development',
            slug='web-development',
            description='Articles about modern web development techniques and frameworks',
            color='#007bff'
        ),
        Category(
            name='Python Programming',
            slug='python-programming',
            description='Python tutorials, tips, and best practices',
            color='#28a745'
        ),
        Category(
            name='SEO & Marketing',
            slug='seo-marketing',
            description='Search engine optimization and digital marketing strategies',
            color='#ffc107'
        ),
        Category(
            name='Technology News',
            slug='technology-news',
            description='Latest updates and trends in technology',
            color='#dc3545'
        )
    ]
    
    for category in categories:
        db.session.add(category)
    
    # Create users
    admin_user = User(
        username='admin',
        email='<EMAIL>',
        first_name='Admin',
        last_name='User',
        bio='Administrator of MyBlog - passionate about technology and writing.',
        is_admin=True
    )
    admin_user.set_password('admin123')
    
    author1 = User(
        username='johndoe',
        email='<EMAIL>',
        first_name='John',
        last_name='Doe',
        bio='Full-stack developer with 5+ years of experience in web technologies.'
    )
    author1.set_password('password123')
    
    author2 = User(
        username='janesmith',
        email='<EMAIL>',
        first_name='Jane',
        last_name='Smith',
        bio='SEO specialist and content marketing expert.'
    )
    author2.set_password('password123')
    
    db.session.add_all([admin_user, author1, author2])
    db.session.commit()
    
    # Create SEO-optimized blog posts
    posts = [
        {
            'title': 'Complete Guide to Flask 3.1.0: Building Modern Web Applications',
            'slug': 'complete-guide-flask-3-1-0-modern-web-applications',
            'content': '''
            <h2>Introduction to Flask 3.1.0</h2>
            <p>Flask 3.1.0 brings exciting new features and improvements that make building modern web applications easier than ever. In this comprehensive guide, we'll explore the latest updates and best practices.</p>
            
            <h3>What's New in Flask 3.1.0</h3>
            <ul>
                <li>Enhanced performance and security features</li>
                <li>Improved error handling and debugging</li>
                <li>Better integration with modern frontend frameworks</li>
                <li>Updated dependencies and compatibility</li>
            </ul>
            
            <h3>Building Your First Flask 3.1.0 Application</h3>
            <p>Let's start by creating a simple Flask application that demonstrates the new features:</p>
            
            <pre><code>from flask import Flask, render_template
app = Flask(__name__)

@app.route('/')
def home():
    return render_template('index.html')

if __name__ == '__main__':
    app.run(debug=True)</code></pre>
    
            <h3>SEO Best Practices for Flask Applications</h3>
            <p>When building Flask applications, it's crucial to implement proper SEO strategies from the beginning. This includes proper meta tags, structured data, and clean URLs.</p>
            
            <h3>Conclusion</h3>
            <p>Flask 3.1.0 provides developers with powerful tools to build scalable, SEO-friendly web applications. By following the practices outlined in this guide, you'll be well on your way to creating successful web projects.</p>
            ''',
            'excerpt': 'Discover the latest features in Flask 3.1.0 and learn how to build modern, SEO-optimized web applications with best practices and practical examples.',
            'author': admin_user,
            'category': categories[0],
            'status': 'published'
        },
        {
            'title': 'Python Best Practices: Writing Clean and Maintainable Code',
            'slug': 'python-best-practices-clean-maintainable-code',
            'content': '''
            <h2>The Importance of Clean Code</h2>
            <p>Writing clean, maintainable Python code is essential for long-term project success. In this article, we'll explore proven best practices that will make your code more readable, efficient, and easier to maintain.</p>
            
            <h3>PEP 8 Style Guide</h3>
            <p>Following PEP 8 conventions ensures your code is consistent with Python community standards:</p>
            <ul>
                <li>Use 4 spaces for indentation</li>
                <li>Limit lines to 79 characters</li>
                <li>Use descriptive variable names</li>
                <li>Follow naming conventions</li>
            </ul>
            
            <h3>Code Organization and Structure</h3>
            <p>Proper project structure makes your code more maintainable and easier to navigate. Here's a recommended structure for Python projects:</p>
            
            <h3>Testing and Documentation</h3>
            <p>Always write tests and documentation alongside your code. This practice ensures reliability and helps other developers understand your work.</p>
            
            <blockquote>
                "Code is read much more often than it is written." - Guido van Rossum
            </blockquote>
            ''',
            'excerpt': 'Learn essential Python best practices for writing clean, maintainable code that follows industry standards and improves team collaboration.',
            'author': author1,
            'category': categories[1],
            'status': 'published'
        },
        {
            'title': 'SEO Fundamentals: Optimizing Your Website for Search Engines',
            'slug': 'seo-fundamentals-optimizing-website-search-engines',
            'content': '''
            <h2>Understanding SEO Basics</h2>
            <p>Search Engine Optimization (SEO) is crucial for increasing your website's visibility and driving organic traffic. This comprehensive guide covers the fundamental principles every website owner should know.</p>
            
            <h3>On-Page SEO Elements</h3>
            <p>On-page SEO involves optimizing individual web pages to rank higher in search results:</p>
            <ul>
                <li><strong>Title Tags:</strong> Craft compelling, keyword-rich titles under 60 characters</li>
                <li><strong>Meta Descriptions:</strong> Write descriptive summaries that encourage clicks</li>
                <li><strong>Header Tags:</strong> Use H1, H2, H3 tags to structure your content</li>
                <li><strong>URL Structure:</strong> Create clean, descriptive URLs</li>
            </ul>
            
            <h3>Technical SEO Considerations</h3>
            <p>Technical SEO ensures search engines can crawl and index your site effectively:</p>
            <ul>
                <li>Site speed optimization</li>
                <li>Mobile responsiveness</li>
                <li>XML sitemaps</li>
                <li>Robots.txt configuration</li>
                <li>Structured data markup</li>
            </ul>
            
            <h3>Content Strategy for SEO</h3>
            <p>Quality content is the foundation of successful SEO. Focus on creating valuable, original content that addresses your audience's needs and questions.</p>
            ''',
            'excerpt': 'Master the fundamentals of SEO with this comprehensive guide covering on-page optimization, technical SEO, and content strategy for better search rankings.',
            'author': author2,
            'category': categories[2],
            'status': 'published'
        },
        {
            'title': 'The Future of Web Development: Trends to Watch in 2024',
            'slug': 'future-web-development-trends-2024',
            'content': '''
            <h2>Emerging Technologies Shaping Web Development</h2>
            <p>The web development landscape continues to evolve rapidly. Here are the key trends and technologies that will define the future of web development in 2024 and beyond.</p>
            
            <h3>AI-Powered Development Tools</h3>
            <p>Artificial Intelligence is revolutionizing how we build websites and applications. From code generation to automated testing, AI tools are becoming indispensable for modern developers.</p>
            
            <h3>Progressive Web Apps (PWAs)</h3>
            <p>PWAs continue to bridge the gap between web and mobile applications, offering native-like experiences with web technologies.</p>
            
            <h3>Serverless Architecture</h3>
            <p>Serverless computing is changing how we deploy and scale applications, offering cost-effective solutions for modern web development.</p>
            
            <h3>WebAssembly (WASM)</h3>
            <p>WebAssembly enables high-performance applications in the browser, opening new possibilities for web-based software.</p>
            ''',
            'excerpt': 'Explore the cutting-edge trends and technologies that will shape the future of web development, from AI-powered tools to serverless architecture.',
            'author': author1,
            'category': categories[3],
            'status': 'published'
        }
    ]
    
    # Create posts with proper timestamps
    for i, post_data in enumerate(posts):
        post = Post(
            title=post_data['title'],
            slug=post_data['slug'],
            content=post_data['content'],
            excerpt=post_data['excerpt'],
            author_id=post_data['author'].id,
            category_id=post_data['category'].id,
            status=post_data['status'],
            published_at=datetime.now(timezone.utc),
            views=50 + (i * 25),  # Simulate different view counts
            likes=10 + (i * 5)    # Simulate different like counts
        )
        db.session.add(post)
    
    db.session.commit()
    
    # Create sample comments
    posts_list = Post.query.all()
    comments_data = [
        {
            'content': 'Great article! This really helped me understand Flask 3.1.0 better.',
            'author': author1,
            'post': posts_list[0]
        },
        {
            'content': 'Thanks for sharing these best practices. Very useful for our team.',
            'author': author2,
            'post': posts_list[1]
        },
        {
            'content': 'Excellent SEO guide! Implementing these strategies right away.',
            'author': author1,
            'post': posts_list[2]
        }
    ]
    
    for comment_data in comments_data:
        comment = Comment(
            content=comment_data['content'],
            author_id=comment_data['author'].id,
            post_id=comment_data['post'].id,
            is_approved=True
        )
        db.session.add(comment)
    
    db.session.commit()
    
    print("✅ Demo data created successfully!")
    print(f"📊 Created:")
    print(f"   - {len(categories)} categories")
    print(f"   - 3 users (1 admin, 2 authors)")
    print(f"   - {len(posts)} SEO-optimized blog posts")
    print(f"   - {len(comments_data)} comments")
    print("\n🔐 Login credentials:")
    print("   Admin: <EMAIL> / admin123")
    print("   Author 1: <EMAIL> / password123")
    print("   Author 2: <EMAIL> / password123")

if __name__ == '__main__':
    # Initialize Flask app
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'demo-secret-key'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///demo_blog.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    
    with app.app_context():
        # Create tables
        db.create_all()
        
        # Create demo data
        create_demo_data()
        
        print("\n🌟 SEO-optimized blog demo is ready!")
        print("🚀 Run 'python app.py' to start the application")
        print("🌐 Visit http://localhost:5000 to see your blog")
        print("\n📈 SEO Features included:")
        print("   ✓ Structured data (JSON-LD)")
        print("   ✓ Open Graph meta tags")
        print("   ✓ Twitter Card support")
        print("   ✓ XML Sitemap (/sitemap.xml)")
        print("   ✓ Robots.txt (/robots.txt)")
        print("   ✓ Breadcrumb navigation")
        print("   ✓ Clean, SEO-friendly URLs")
        print("   ✓ Responsive design")
        print("   ✓ Fast loading times")
        print("   ✓ Mobile-optimized")
