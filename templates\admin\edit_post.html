{% extends "layouts/base.html" %}

{% block title %}{% if post %}Edit Post{% else %}New Post{% endif %} - Admin{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2">
            <div class="admin-sidebar p-3">
                <h5 class="text-white mb-3">
                    <i class="fas fa-cog me-2"></i>Admin Panel
                </h5>
                <nav class="nav flex-column">
                    <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link active" href="{{ url_for('admin.posts') }}">
                        <i class="fas fa-blog me-2"></i>Posts
                    </a>
                    <a class="nav-link" href="{{ url_for('admin.categories') }}">
                        <i class="fas fa-tags me-2"></i>Categories
                    </a>
                    <a class="nav-link" href="{{ url_for('admin.comments') }}">
                        <i class="fas fa-comments me-2"></i>Comments
                    </a>
                    <a class="nav-link" href="{{ url_for('admin.users') }}">
                        <i class="fas fa-users me-2"></i>Users
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>{% if post %}Edit Post{% else %}New Post{% endif %}</h1>
                <a href="{{ url_for('admin.posts') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Posts
                </a>
            </div>

            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Post Content</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="title" class="form-label">Title *</label>
                                    <input type="text" class="form-control" id="title" name="title" 
                                           value="{{ post.title if post else '' }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="excerpt" class="form-label">Excerpt</label>
                                    <textarea class="form-control" id="excerpt" name="excerpt" rows="3" 
                                              placeholder="Brief description of the post...">{{ post.excerpt if post else '' }}</textarea>
                                    <div class="form-text">Optional. If left blank, it will be auto-generated from content.</div>
                                </div>

                                <div class="mb-3">
                                    <label for="content" class="form-label">Content *</label>
                                    <div class="editor-toolbar">
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="formatText('bold')">
                                            <i class="fas fa-bold"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="formatText('italic')">
                                            <i class="fas fa-italic"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="formatText('underline')">
                                            <i class="fas fa-underline"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="formatText('insertOrderedList')">
                                            <i class="fas fa-list-ol"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="formatText('insertUnorderedList')">
                                            <i class="fas fa-list-ul"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="insertLink()">
                                            <i class="fas fa-link"></i>
                                        </button>
                                    </div>
                                    <div class="editor-content" contenteditable="true" id="content-editor">
                                        {{ post.content|safe if post else '' }}
                                    </div>
                                    <textarea name="content" id="content" style="display: none;" required>{{ post.content if post else '' }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Publish</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="draft" {% if post and post.status == 'draft' %}selected{% endif %}>Draft</option>
                                        <option value="published" {% if post and post.status == 'published' %}selected{% endif %}>Published</option>
                                    </select>
                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        {% if post %}Update Post{% else %}Create Post{% endif %}
                                    </button>
                                    {% if post %}
                                    <a href="{{ url_for('main.post_detail', slug=post.slug) }}" 
                                       class="btn btn-outline-primary" target="_blank">
                                        <i class="fas fa-eye me-2"></i>Preview
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Category</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Category</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">No Category</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" 
                                                {% if post and post.category_id == category.id %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">Featured Image</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="featured_image" name="featured_image" 
                                           accept="image/*" onchange="previewImage(this)">
                                    <div class="form-text">Upload a featured image for this post.</div>
                                </div>
                                
                                <div id="image-preview" class="text-center">
                                    {% if post and post.featured_image %}
                                    <img src="{{ url_for('static', filename='uploads/' + post.featured_image) }}" 
                                         alt="Featured Image" class="img-fluid rounded" style="max-height: 200px;">
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Rich text editor functionality
const editor = document.getElementById('content-editor');
const hiddenTextarea = document.getElementById('content');

// Update hidden textarea when editor content changes
editor.addEventListener('input', function() {
    hiddenTextarea.value = this.innerHTML;
});

// Format text functions
function formatText(command) {
    document.execCommand(command, false, null);
    editor.focus();
}

function insertLink() {
    const url = prompt('Enter URL:');
    if (url) {
        document.execCommand('createLink', false, url);
    }
    editor.focus();
}

// Image preview
function previewImage(input) {
    const preview = document.getElementById('image-preview');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="img-fluid rounded" style="max-height: 200px;">`;
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

// Form submission
document.querySelector('form').addEventListener('submit', function() {
    // Ensure content is synced
    hiddenTextarea.value = editor.innerHTML;
});

// Initialize editor content
if (hiddenTextarea.value) {
    editor.innerHTML = hiddenTextarea.value;
}
</script>
{% endblock %}
