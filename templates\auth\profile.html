{% extends "layouts/base.html" %}

{% block title %}Profile - Blog{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <img src="{{ url_for('static', filename='images/' + current_user.avatar) }}" 
                         alt="Avatar" class="rounded-circle mb-3" width="120" height="120"
                         onerror="this.src='{{ url_for('static', filename='images/default-avatar.png') }}'">
                    <h4>{{ current_user.get_full_name() }}</h4>
                    <p class="text-muted">@{{ current_user.username }}</p>
                    {% if current_user.is_admin %}
                        <span class="badge bg-danger">Admin</span>
                    {% endif %}
                    <hr>
                    <div class="row text-center">
                        <div class="col">
                            <h5>{{ current_user.posts|length }}</h5>
                            <small class="text-muted">Posts</small>
                        </div>
                        <div class="col">
                            <h5>{{ current_user.comments|length }}</h5>
                            <small class="text-muted">Comments</small>
                        </div>
                    </div>
                    <hr>
                    <a href="{{ url_for('auth.edit_profile') }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>Profile Information</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Full Name:</strong></div>
                        <div class="col-sm-9">{{ current_user.get_full_name() }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Username:</strong></div>
                        <div class="col-sm-9">{{ current_user.username }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Email:</strong></div>
                        <div class="col-sm-9">{{ current_user.email }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Member Since:</strong></div>
                        <div class="col-sm-9">{{ current_user.created_at.strftime('%B %d, %Y') }}</div>
                    </div>
                    {% if current_user.bio %}
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Bio:</strong></div>
                        <div class="col-sm-9">{{ current_user.bio }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Posts -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-blog me-2"></i>Recent Posts</h5>
                </div>
                <div class="card-body">
                    {% if current_user.posts %}
                        {% for post in current_user.posts[:5] %}
                        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                            <div>
                                <h6 class="mb-1">
                                    <a href="{{ url_for('main.post_detail', slug=post.slug) }}" class="text-decoration-none">
                                        {{ post.title }}
                                    </a>
                                </h6>
                                <small class="text-muted">{{ post.created_at.strftime('%B %d, %Y') }}</small>
                            </div>
                            <span class="badge bg-{{ 'success' if post.status == 'published' else 'warning' }}">
                                {{ post.status.title() }}
                            </span>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No posts yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
