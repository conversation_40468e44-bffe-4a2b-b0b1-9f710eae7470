/* Custom CSS for Blog Application */

/* Global Styles */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    padding-top: 76px; /* Account for fixed navbar */
}

/* Navigation */
.navbar-brand {
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    min-height: 400px;
    display: flex;
    align-items: center;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-img-top {
    border-radius: 10px 10px 0 0;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Buttons */
.btn {
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Post Content */
.post-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.post-content h1, .post-content h2, .post-content h3,
.post-content h4, .post-content h5, .post-content h6 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.post-content p {
    margin-bottom: 1.5rem;
}

.post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 1rem 0;
}

.post-content blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    background-color: var(--light-color);
    padding: 1rem;
    border-radius: 0 8px 8px 0;
}

.post-content code {
    background-color: var(--light-color);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.post-content pre {
    background-color: var(--dark-color);
    color: white;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
}

/* Comments */
.comment {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}

.comment:last-child {
    border-bottom: none;
}

/* Social Share Buttons */
.social-share-buttons .btn {
    border-radius: 20px;
    font-size: 0.9rem;
}

.social-links a {
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-color) !important;
}

/* Forms */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    border: 2px solid #e9ecef;
    border-right: none;
    background-color: var(--light-color);
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
    border-radius: 15px;
}

/* Pagination */
.pagination .page-link {
    border-radius: 20px;
    margin: 0 2px;
    border: none;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Alerts */
.alert {
    border-radius: 10px;
    border: none;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Admin Styles */
.admin-sidebar {
    background-color: var(--dark-color);
    min-height: calc(100vh - 76px);
}

.admin-sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 5px;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.admin-stats .card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
}

.admin-stats .card-body {
    padding: 1.5rem;
}

.admin-stats .card h3 {
    font-size: 2.5rem;
    font-weight: bold;
}

/* Rich Text Editor */
.editor-toolbar {
    background-color: var(--light-color);
    border: 1px solid #ddd;
    border-bottom: none;
    padding: 0.5rem;
    border-radius: 8px 8px 0 0;
}

.editor-content {
    min-height: 300px;
    border: 1px solid #ddd;
    border-radius: 0 0 8px 8px;
    padding: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 300px;
        text-align: center;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .card-columns {
        column-count: 1;
    }
    
    .social-share-buttons .btn {
        margin-bottom: 0.5rem;
        width: 100%;
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .admin-sidebar {
        min-height: auto;
    }
}

@media (max-width: 576px) {
    body {
        padding-top: 70px;
    }
    
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 0.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.border-radius-lg {
    border-radius: 15px;
}

.transition-all {
    transition: all 0.3s ease;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .form-control {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .form-control:focus {
        background-color: #4a5568;
        border-color: var(--primary-color);
        color: #e2e8f0;
    }
}
